\documentclass[10pt,a4paper]{article}

%% paquetes básicos
\usepackage[utf8]{inputenc}
\usepackage[spanish,es-tabla]{babel}
\usepackage{a4wide,color,verbatim,Sweave,url,xargs,amsmath,booktabs,longtable}
\usepackage{graphicx,float}
\usepackage{tikz,xcolor}
\usepackage{pgfplots}
\usepackage{enumitem}

%% bibliotecas TikZ
\usetikzlibrary{automata,positioning,calc,arrows}
\pgfplotsset{compat=1.18}

%% entornos para exams
\newenvironment{question}{\item}{}
\newenvironment{solution}{\comment}{\endcomment}
\newenvironment{answerlist}{\renewcommand{\labelenumii}{(\alph{enumii})}\begin{enumerate}}{\end{enumerate}}

%% comandos para metadatos exams
\newcommand{\exname}[1]{\def\@exname{#1}}
\newcommand{\extype}[1]{\def\@extype{#1}}
\newcommand{\exsolution}[1]{\def\@exsolution{#1}}
\newcommand{\exshuffle}[1]{\def\@exshuffle{#1}}
\newcommand{\exsection}[1]{\def\@exsection{#1}}

%% configuración párrafos
\setlength{\parskip}{0.7ex plus0.1ex minus0.1ex}
\setlength{\parindent}{0em}

\begin{document}
\SweaveOpts{concordance=TRUE}

\begin{enumerate}

<<echo=FALSE, results=hide>>=
# Configuración inicial
library(exams)

# Configuración TikZ
typ <- match_exams_device()
if(match_exams_call() == "exams2nops") {
  typ <- "png"  # NOPS requiere PNG en lugar de TEX para TikZ
}

# Semilla aleatoria
set.seed(sample(1:100000, 1))

# Funciones para crear gráficas TikZ - VERSIÓN COMPLETA
crear_grafica_A <- function(h, color) {
  paste0(
    "\\begin{tikzpicture}\n",
    "\\begin{axis}[\n",
    "  width=5cm, height=4cm,\n",
    "  xlabel={Angulo $\\alpha$},\n",
    "  ylabel={Distancia PK},\n",
    "  xmin=0, xmax=90,\n",
    "  ymin=0, ymax=", h * 3, ",\n",
    "  grid=major, axis lines=left\n",
    "]\n",
    "\\addplot[", color, ", very thick, domain=0:90] {", h, "};\n",
    "\\end{axis}\n",
    "\\end{tikzpicture}"
  )
}

crear_grafica_B <- function(h, color) {
  paste0(
    "\\begin{tikzpicture}\n",
    "\\begin{axis}[\n",
    "  width=5cm, height=4cm,\n",
    "  xlabel={Angulo $\\alpha$},\n",
    "  ylabel={Distancia PK},\n",
    "  xmin=10, xmax=90,\n",
    "  ymin=", h, ", ymax=", h * 6, ",\n",
    "  grid=major, axis lines=left\n",
    "]\n",
    "\\addplot[", color, ", very thick, domain=10:89, samples=200] {", h, "/sin(x)};\n",
    "\\end{axis}\n",
    "\\end{tikzpicture}"
  )
}

crear_grafica_C <- function(h, color) {
  # Función tipo arcocoseno con rangos específicos diferentes
  # y = 20 * (1 - x/90)^0.4 + 2 para rango [0,90] -> [22,2]
  paste0(
    "\\begin{tikzpicture}\n",
    "\\begin{axis}[\n",
    "  width=5cm, height=4cm,\n",
    "  xlabel={Angulo $\\alpha$},\n",
    "  ylabel={Distancia PK},\n",
    "  xmin=0, xmax=90,\n",
    "  ymin=0, ymax=25,\n",
    "  xtick={0,15,30,45,60,75,90},\n",
    "  ytick={0,5,10,15,20,25},\n",
    "  grid=major,\n",
    "  grid style={gray!30},\n",
    "  axis lines=left,\n",
    "  samples=300,\n",
    "  smooth\n",
    "]\n",
    "% Función tipo arcocoseno con rangos específicos muy diferentes\n",
    "\\addplot[", color, ", very thick, domain=0:89, samples=300] {20 * (1 - x/90)^0.4 + 2};\n",
    "\\end{axis}\n",
    "\\end{tikzpicture}"
  )
}

crear_grafica_D <- function(h, color) {
  paste0(
    "\\begin{tikzpicture}\n",
    "\\begin{axis}[\n",
    "  width=5cm, height=4cm,\n",
    "  xlabel={Angulo $\\alpha$},\n",
    "  ylabel={Distancia PK},\n",
    "  xmin=0, xmax=90,\n",
    "  ymin=0, ymax=", h * 4, ",\n",
    "  grid=major, axis lines=left\n",
    "]\n",
    "\\addplot[", color, ", very thick, domain=0:90] {", h * 2, "};\n",
    "\\end{axis}\n",
    "\\end{tikzpicture}"
  )
}

# Función generar_datos() simplificada
generar_datos <- function() {
  h_valor <- sample(c(2, 3, 4, 5), 1)
  color_curva <- sample(c("blue", "red", "green", "purple"), 1)

  punto_inicial <- "Q"
  punto_final <- "T"
  punto_movil <- "K"
  punto_fijo <- "P"

  # Generar las 4 opciones gráficas CON NOMBRES ÚNICOS
  opciones_graficas <- list()
  opciones_graficas$A <- crear_grafica_A(h_valor, color_curva)
  opciones_graficas$B <- crear_grafica_B(h_valor, color_curva)
  opciones_graficas$C <- crear_grafica_C(h_valor, color_curva)  # HIPERBÓLICA NUEVA
  opciones_graficas$D <- crear_grafica_D(h_valor, color_curva)

  # La respuesta correcta es siempre B
  solutions <- c(FALSE, TRUE, FALSE, FALSE)

  return(list(
    h = h_valor,
    color = color_curva,
    punto_inicial = punto_inicial,
    punto_final = punto_final,
    punto_movil = punto_movil,
    punto_fijo = punto_fijo,
    graficas = opciones_graficas,
    solutions = solutions
  ))
}

# Generar datos para este ejercicio
datos <- generar_datos()
@

\begin{question}

Un punto \Sexpr{datos$punto_movil} se mueve de un extremo a otro del segmento \Sexpr{datos$punto_inicial}\Sexpr{datos$punto_final}.

El ángulo $\alpha$ y la medida $h$ se relacionan mediante $\sin(\alpha) = \frac{h}{\Sexpr{datos$punto_movil}\Sexpr{datos$punto_fijo}}$, de donde se deduce $\Sexpr{datos$punto_movil}\Sexpr{datos$punto_fijo} = \frac{h}{\sin(\alpha)} = h \times \csc(\alpha)$.

¿Cuál gráfica muestra las distancias $\Sexpr{datos$punto_movil}\Sexpr{datos$punto_fijo}$ cuando \Sexpr{datos$punto_movil} se mueve sobre el segmento?

\begin{answerlist}
\item
<<echo=FALSE, results=tex>>=
include_tikz(datos$graficas$A, name = "grafica_A_v3",
  format = typ, width = "5cm", packages = c("tikz", "pgfplots"))
@

\item
<<echo=FALSE, results=tex>>=
include_tikz(datos$graficas$B, name = "grafica_B_v3",
  format = typ, width = "5cm", packages = c("tikz", "pgfplots"))
@

\item
<<echo=FALSE, results=tex>>=
include_tikz(datos$graficas$C, name = "grafica_C_v3",
  format = typ, width = "5cm", packages = c("tikz", "pgfplots"))
@

\item
<<echo=FALSE, results=tex>>=
include_tikz(datos$graficas$D, name = "grafica_D_v3",
  format = typ, width = "5cm", packages = c("tikz", "pgfplots"))
@

\end{answerlist}

\end{question}

\begin{solution}

La relación es $\Sexpr{datos$punto_movil}\Sexpr{datos$punto_fijo} = h \times \csc(\alpha) = \frac{h}{\sin(\alpha)}$.

Cuando $\alpha$ es pequeño: $\sin(\alpha) \approx 0$, entonces $\csc(\alpha) \to \infty$.
Cuando $\alpha \to 90$ grados: $\sin(\alpha) \to 1$, entonces $\csc(\alpha) \to 1$.

La gráfica debe mostrar una función cosecante decreciente.

La respuesta correcta es B.

<<echo=FALSE, results=tex>>=
answerlist(ifelse(datos$solutions, "Verdadero", "Falso"))
@

\end{solution}

%% META-INFORMATION
\exname{Trigonometria Punto Movil v3}
\extype{schoice}
\exsolution{\Sexpr{mchoice2string(datos$solutions)}}
\exshuffle{TRUE}
\exsection{Geometria Metrica}

\end{enumerate}
\end{document}
