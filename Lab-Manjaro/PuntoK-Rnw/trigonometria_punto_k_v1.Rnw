\documentclass[10pt,a4paper]{article}

%% paquetes básicos
\usepackage[utf8]{inputenc}
\usepackage[spanish]{babel}
\usepackage{a4wide,color,verbatim,Sweave,url,xargs,amsmath,booktabs,longtable}
\usepackage{graphicx,float}
\usepackage{tikz,xcolor}
\usepackage{pgfplots}
\usepackage{enumitem}

%% bibliotecas TikZ
\usetikzlibrary{automata,positioning,calc,arrows}
\pgfplotsset{compat=1.18}

%% entornos para exams
\newenvironment{question}{\item}{}
\newenvironment{solution}{\comment}{\endcomment}
\newenvironment{answerlist}{\renewcommand{\labelenumii}{(\alph{enumii})}\begin{enumerate}}{\end{enumerate}}

%% comandos para metadatos exams
\newcommand{\exname}[1]{\def\@exname{#1}}
\newcommand{\extype}[1]{\def\@extype{#1}}
\newcommand{\exsolution}[1]{\def\@exsolution{#1}}
\newcommand{\exshuffle}[1]{\def\@exshuffle{#1}}
\newcommand{\exsection}[1]{\def\@exsection{#1}}

%% configuración párrafos
\setlength{\parskip}{0.7ex plus0.1ex minus0.1ex}
\setlength{\parindent}{0em}

\begin{document}
\SweaveOpts{concordance=TRUE}

\begin{enumerate}

<<echo=FALSE, results=hide>>=
# Configuración inicial
library(exams)
library(digest)
library(testthat)

# Configuración TikZ
typ <- match_exams_device()
if(match_exams_call() == "exams2nops") typ <- "tex"

# Semilla aleatoria
set.seed(sample(1:100000, 1))

# Función generar_datos() con aleatorización robusta
generar_datos <- function() {
  # Parámetros aleatorios más diversos
  h_valor <- sample(c(1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5, 5.5, 6), 1)
  color_curva <- sample(c("blue", "red", "green", "purple", "orange", "brown", "cyan", "magenta"), 1)

  # Contextos variables más diversos
  puntos_iniciales <- c("Q", "P", "R", "S", "A", "B", "C", "D", "E", "F")
  puntos_finales <- c("T", "U", "V", "W", "X", "Y", "Z", "M", "N", "O")
  puntos_moviles <- c("K", "M", "N", "L", "J", "I", "H", "G", "F", "E")
  puntos_fijos <- c("P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y")

  punto_inicial <- sample(puntos_iniciales, 1)
  punto_final <- sample(puntos_finales, 1)
  punto_movil <- sample(puntos_moviles, 1)
  punto_fijo <- sample(puntos_fijos, 1)

  # Asegurar que los puntos sean diferentes
  while(punto_inicial == punto_final || punto_inicial == punto_movil ||
        punto_inicial == punto_fijo || punto_final == punto_movil ||
        punto_final == punto_fijo || punto_movil == punto_fijo) {
    punto_inicial <- sample(puntos_iniciales, 1)
    punto_final <- sample(puntos_finales, 1)
    punto_movil <- sample(puntos_moviles, 1)
    punto_fijo <- sample(puntos_fijos, 1)
  }

  # Variaciones en las alturas de las gráficas constantes
  altura_A <- sample(c(h_valor * 0.5, h_valor * 0.8, h_valor * 1.2, h_valor * 1.5, h_valor * 2), 1)
  altura_D <- sample(c(h_valor * 0.3, h_valor * 0.7, h_valor * 1.3, h_valor * 1.8, h_valor * 2.5), 1)

  # Asegurar que las alturas constantes sean diferentes
  while(abs(altura_A - altura_D) < 0.5) {
    altura_D <- sample(c(h_valor * 0.3, h_valor * 0.7, h_valor * 1.3, h_valor * 1.8, h_valor * 2.5), 1)
  }

  # Generar las 4 opciones gráficas
  opciones_graficas <- list()

  # Opción A: Línea horizontal constante
  opciones_graficas$A <- crear_grafica_constante(altura_A, color_curva, "A")

  # Opción B: Cosecante decreciente (CORRECTA)
  opciones_graficas$B <- crear_grafica_cosecante(h_valor, color_curva, "B")

  # Opción C: Cotangente (distractor plausible)
  opciones_graficas$C <- crear_grafica_cotangente(h_valor, color_curva, "C")

  # Opción D: Línea horizontal constante (diferente altura)
  opciones_graficas$D <- crear_grafica_constante(altura_D, color_curva, "D")

  # Orden aleatorio de opciones
  orden <- sample(c("A", "B", "C", "D"))
  respuesta_correcta <- which(orden == "B")

  # Sistema avanzado de distractores
  permitir_duplicados <- sample(c(TRUE, FALSE), 1, prob = c(0.3, 0.7))

  # Crear opciones textuales con justificaciones variables
  if(permitir_duplicados && runif(1) < 0.3) {
    # 30% probabilidad de incluir justificaciones alternativas
    opciones_texto <- c(
      "A (la distancia permanece constante)",
      "B (la distancia disminuye según la función cosecante)",
      "C (la distancia sigue el patrón de la cotangente)",
      "D (la distancia se mantiene en un valor fijo diferente)"
    )
  } else {
    # Modo tradicional: solo letras
    opciones_texto <- c("A", "B", "C", "D")
  }

  # Soluciones (TRUE para la correcta)
  solutions <- rep(FALSE, 4)
  solutions[respuesta_correcta] <- TRUE

  # Variaciones adicionales en el contexto del problema
  variacion_contexto <- sample(1:5, 1)

  return(list(
    h = h_valor,
    color = color_curva,
    punto_inicial = punto_inicial,
    punto_final = punto_final,
    punto_movil = punto_movil,
    punto_fijo = punto_fijo,
    altura_A = altura_A,
    altura_D = altura_D,
    graficas = opciones_graficas,
    orden = orden,
    opciones = opciones_texto,
    solutions = solutions,
    respuesta_correcta = respuesta_correcta,
    variacion_contexto = variacion_contexto,
    permitir_duplicados = permitir_duplicados
  ))
}

# Funciones para crear gráficas TikZ con pgfplots (configuración corregida)
crear_grafica_constante <- function(altura, color, etiqueta) {
  ymax <- max(altura * 2, 8)
  paste0(
    "\\begin{tikzpicture}\n",
    "\\begin{axis}[\n",
    "  width=4cm, height=3cm,\n",
    "  xlabel={\\'{A}ngulo $\\alpha$},\n",
    "  ylabel={Distancia $PK$},\n",
    "  xmin=0, xmax=90,\n",
    "  ymin=0, ymax=", ymax, ",\n",
    "  xtick={0,30,60,90},\n",
    "  ytick={", round(altura, 1), "},\n",
    "  grid=major,\n",
    "  grid style={gray!30},\n",
    "  axis lines=left,\n",
    "  tick label style={font=\\footnotesize}\n",
    "]\n",
    "\\addplot[", color, ", very thick, domain=0:90] {", round(altura, 2), "};\n",
    "\\end{axis}\n",
    "\\end{tikzpicture}"
  )
}

crear_grafica_cosecante <- function(h, color, etiqueta) {
  ymax <- h * 5
  paste0(
    "\\begin{tikzpicture}\n",
    "\\begin{axis}[\n",
    "  width=4cm, height=3cm,\n",
    "  xlabel={\\'{A}ngulo $\\alpha$},\n",
    "  ylabel={Distancia $PK$},\n",
    "  xmin=10, xmax=90,\n",
    "  ymin=", round(h * 0.9, 1), ", ymax=", round(ymax, 1), ",\n",
    "  xtick={15,30,45,60,75,90},\n",
    "  ytick={", round(h, 1), ",", round(h * 2, 1), ",", round(h * 3, 1), "},\n",
    "  grid=major,\n",
    "  grid style={gray!30},\n",
    "  axis lines=left,\n",
    "  tick label style={font=\\footnotesize},\n",
    "  restrict y to domain=", round(h * 0.9, 1), ":", round(ymax, 1), "\n",
    "]\n",
    "\\addplot[", color, ", very thick, domain=10:89, samples=200, smooth] {", round(h, 2), "/sin(x)};\n",
    "\\end{axis}\n",
    "\\end{tikzpicture}"
  )
}

crear_grafica_cotangente <- function(h, color, etiqueta) {
  ymax <- h * 6  # Aumentar para mostrar mejor el comportamiento inicial
  paste0(
    "\\begin{tikzpicture}\n",
    "\\begin{axis}[\n",
    "  width=4cm, height=3cm,\n",
    "  xlabel={\\'{A}ngulo $\\alpha$},\n",
    "  ylabel={Distancia $PK$},\n",
    "  xmin=5, xmax=90,\n",
    "  ymin=0, ymax=", round(ymax, 1), ",\n",
    "  xtick={15,30,45,60,75,90},\n",
    "  ytick={0,", round(h, 1), ",", round(h * 2, 1), ",", round(h * 4, 1), "},\n",
    "  grid=major,\n",
    "  grid style={gray!30},\n",
    "  axis lines=left,\n",
    "  tick label style={font=\\footnotesize},\n",
    "  restrict y to domain=0:", round(ymax, 1), "\n",
    "]\n",
    "% Función cotangente escalada que llega a cero en 90°\n",
    "\\addplot[", color, ", very thick, domain=5:89.5, samples=300, smooth] {", round(h * 2, 2), " * cot(x)};\n",
    "\\end{axis}\n",
    "\\end{tikzpicture}"
  )
}

# Función adicional para crear gráfica exponencial decreciente (distractor alternativo)
crear_grafica_exponencial <- function(h, color, etiqueta) {
  paste0(
    "\\begin{tikzpicture}\n",
    "\\begin{axis}[\n",
    "  width=4cm, height=3cm,\n",
    "  xlabel={\\'{A}ngulo $\\alpha$},\n",
    "  ylabel={Distancia $PK$},\n",
    "  xmin=0, xmax=90,\n",
    "  ymin=0, ymax=", round(h * 3, 1), ",\n",
    "  xtick={0,30,60,90},\n",
    "  ytick={", round(h, 1), ",", round(h * 2, 1), "},\n",
    "  grid=major,\n",
    "  grid style={gray!30},\n",
    "  axis lines=left,\n",
    "  tick label style={font=\\footnotesize}\n",
    "]\n",
    "\\addplot[", color, ", very thick, domain=0:90, samples=100, smooth] {", round(h * 2.5, 2), " * exp(-x/30) + ", round(h * 0.5, 2), "};\n",
    "\\end{axis}\n",
    "\\end{tikzpicture}"
  )
}

# Generar datos para este ejercicio
datos <- generar_datos()

# Prueba de diversidad de versiones
test_that("Prueba de diversidad de versiones", {
  versiones <- list()
  for(i in 1:1000) {
    datos_test <- generar_datos()
    versiones[[i]] <- digest::digest(datos_test)
  }

  n_versiones_unicas <- length(unique(versiones))
  expect_true(n_versiones_unicas >= 300,
              info = paste("Solo se generaron", n_versiones_unicas,
                          "versiones únicas. Se requieren al menos 300."))
})
@

\begin{question}

Un punto \Sexpr{datos$punto_movil} se mueve de un extremo a otro del segmento \Sexpr{datos$punto_inicial}\Sexpr{datos$punto_final} que se muestra en la gráfica.

% Diagrama geométrico simplificado
\begin{center}
\begin{tikzpicture}[scale=1.0]
% Segmento horizontal QT
\draw[thick] (0,0) -- (4,0);
\node[below] at (0,0) {\Sexpr{datos$punto_inicial}};
\node[below] at (4,0) {\Sexpr{datos$punto_final}};
% Punto K móvil
\fill[blue] (2,0) circle (2pt);
\node[below] at (2,0) {\Sexpr{datos$punto_movil}};
% Punto P fijo
\fill[red] (2,1.5) circle (2pt);
\node[above] at (2,1.5) {\Sexpr{datos$punto_fijo}};
% Línea vertical h
\draw[dashed, red] (2,0) -- (2,1.5);
\node[right] at (2,0.75) {$h$};
% Línea KP
\draw[thick, blue] (2,0) -- (2,1.5);
% Ángulo alpha
\draw[thick] (1.5,0) arc (180:90:0.5);
\node at (1.3,0.3) {$\alpha$};
\end{tikzpicture}
\end{center}

El ángulo $\alpha$ y la medida $h$ se relacionan mediante la razón trigonométrica $\sen(\alpha) = \frac{h}{\Sexpr{datos$punto_movil}\Sexpr{datos$punto_fijo}}$, de donde se deduce la distancia entre \Sexpr{datos$punto_movil} y \Sexpr{datos$punto_fijo} como $\Sexpr{datos$punto_movil}\Sexpr{datos$punto_fijo} = \frac{h}{\sen(\alpha)}$ o $\Sexpr{datos$punto_movil}\Sexpr{datos$punto_fijo} = h \times \csc(\alpha)$.

¿Cuál es la gráfica que muestra las distancias $\Sexpr{datos$punto_movil}\Sexpr{datos$punto_fijo}$, cada vez que \Sexpr{datos$punto_movil} se mueve sobre el segmento \Sexpr{datos$punto_inicial}\Sexpr{datos$punto_final}?

\begin{tabular}{cc}
A & B \\

<<echo=FALSE, results=tex>>=
include_tikz(datos$graficas$A, name = "grafica_A",
  format = typ, width = "4cm", packages = c("tikz", "pgfplots"))
@

&

<<echo=FALSE, results=tex>>=
include_tikz(datos$graficas$B, name = "grafica_B",
  format = typ, width = "4cm", packages = c("tikz", "pgfplots"))
@

\\
C & D \\

<<echo=FALSE, results=tex>>=
include_tikz(datos$graficas$C, name = "grafica_C",
  format = typ, width = "4cm", packages = c("tikz", "pgfplots"))
@

&

<<echo=FALSE, results=tex>>=
include_tikz(datos$graficas$D, name = "grafica_D",
  format = typ, width = "4cm", packages = c("tikz", "pgfplots"))
@

\end{tabular}

<<echo=FALSE, results=tex>>=
answerlist(datos$opciones)
@

\end{question}

\begin{solution}

La relación matemática fundamental es $\Sexpr{datos$punto_movil}\Sexpr{datos$punto_fijo} = h \times \csc(\alpha) = \frac{h}{\sen(\alpha)}$.

\textbf{Análisis del comportamiento de la función:}

Cuando \Sexpr{datos$punto_movil} se mueve sobre el segmento \Sexpr{datos$punto_inicial}\Sexpr{datos$punto_final}:

\begin{itemize}
\item \textbf{Al inicio} (cerca de \Sexpr{datos$punto_inicial}): $\alpha$ es muy pequeño $\Rightarrow$ $\sen(\alpha) \approx 0$ $\Rightarrow$ $\csc(\alpha) \to \infty$ $\Rightarrow$ $\Sexpr{datos$punto_movil}\Sexpr{datos$punto_fijo}$ muy grande

\item \textbf{En posiciones intermedias}: $\alpha$ aumenta gradualmente $\Rightarrow$ $\sen(\alpha)$ aumenta $\Rightarrow$ $\csc(\alpha)$ disminuye $\Rightarrow$ $\Sexpr{datos$punto_movil}\Sexpr{datos$punto_fijo}$ disminuye

\item \textbf{Al final} (cerca de \Sexpr{datos$punto_final}): $\alpha$ se acerca a $90°$ $\Rightarrow$ $\sen(\alpha) \to 1$ $\Rightarrow$ $\csc(\alpha) \to 1$ $\Rightarrow$ $\Sexpr{datos$punto_movil}\Sexpr{datos$punto_fijo} = h$
\end{itemize}

\textbf{Análisis de las opciones:}

\begin{itemize}
\item \textbf{Opción A}: Línea horizontal constante - \textbf{Incorrecta}. La distancia no permanece constante.
\item \textbf{Opción B}: Función cosecante decreciente - \textbf{Correcta}. Muestra el comportamiento esperado de $\csc(\alpha)$.
\item \textbf{Opción C}: Función cotangente - \textbf{Incorrecta}. Aunque $\cot(\alpha)$ también decrece y llega a cero en $90°$, no representa la relación geométrica $KP = h/\sen(\alpha)$. La cotangente es $\cot(\alpha) = \cos(\alpha)/\sen(\alpha)$, que es diferente a la cosecante $\csc(\alpha) = 1/\sen(\alpha)$.
\item \textbf{Opción D}: Línea horizontal en otro nivel - \textbf{Incorrecta}. Similar al error de la opción A.
\end{itemize}

Por lo tanto, la gráfica debe mostrar una función cosecante decreciente que empieza con valores muy altos y se aproxima a $h$ cuando $\alpha$ se acerca a $90°$.

\textbf{La respuesta correcta es la opción B.}

<<echo=FALSE, results=tex>>=
answerlist(ifelse(datos$solutions, "Verdadero", "Falso"))
@

\end{solution}

%% META-INFORMATION
\exname{Trigonometría Punto Móvil}
\extype{schoice}
\exsolution{\Sexpr{mchoice2string(datos$solutions)}}
\exshuffle{TRUE}
\exsection{Geometría Métrica}

\end{enumerate}
\end{document}
